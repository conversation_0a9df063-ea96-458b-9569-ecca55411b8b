package com.payne.server.banknote.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.payne.server.banknote.entity.PjOSendformItem;
import com.payne.server.banknote.mapper.PjOSendformItemMapper;
import com.payne.server.banknote.service.PjOSendformItemService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 送评单明细Service实现类
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class PjOSendformItemServiceImpl extends ServiceImpl<PjOSendformItemMapper, PjOSendformItem> 
        implements PjOSendformItemService {
    
    @Override
    public List<PjOSendformItem> listBySendnum(String sendnum) {
        LambdaQueryWrapper<PjOSendformItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PjOSendformItem::getSendnum, sendnum)
               .orderByAsc(PjOSendformItem::getSeqno);
        return baseMapper.selectList(wrapper);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteBySendnum(String sendnum) {
        LambdaQueryWrapper<PjOSendformItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PjOSendformItem::getSendnum, sendnum);
        return baseMapper.delete(wrapper) >= 0;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBatchItems(String sendnum, List<PjOSendformItem> items) {
        if (items == null || items.isEmpty()) {
            return true;
        }

        try {
            // 使用MyBatis-Plus的方法删除
            LambdaQueryWrapper<PjOSendformItem> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(PjOSendformItem::getSendnum, sendnum);
            int deleteCount = baseMapper.delete(wrapper);

            // 根据quantity字段展开钱币列表
            List<PjOSendformItem> expandedItems = new ArrayList<>();

            for (PjOSendformItem originalItem : items) {
                // 获取数量，默认为1
                Integer quantity = originalItem.getQuantity();
                if (quantity == null || quantity <= 0) {
                    quantity = 1;
                }

                // 根据数量生成对应数量的钱币记录
                for (int i = 0; i < quantity; i++) {
                    // 创建新的钱币记录（深拷贝）
                    PjOSendformItem newItem = copyItem(originalItem);

                    // 重置ID，让数据库自动生成
                    newItem.setId(null);

                    // 设置数量为1（每条记录代表一个钱币）
                    newItem.setQuantity(1);

                    // 如果有送评单号且数量大于1，需要为送评单号生成唯一编号
                    /*if (quantity > 1 && newItem.getDiyCode() != null && !newItem.getDiyCode().trim().isEmpty()) {
                        // 在原编号后添加序号后缀
                        String originalNumber = newItem.getDiyCode();
                        newItem.setDiyCode(originalNumber + "-" + String.format("%02d", i + 1));
                    }*/

                    expandedItems.add(newItem);
                }
            }

            // 设置送评单号和必要字段
            for (int i = 0; i < expandedItems.size(); i++) {
                PjOSendformItem item = expandedItems.get(i);
                item.setSendnum(sendnum);
                item.setSeqno(i + 1);
                //真伪鉴定
//                item.setAuthenticity("真");
                //评级打分
//                item.setGradeScore("Superb Gem Unc68");
                //评分备注
                item.setScoreRemarks("");
                //特殊标记
                item.setSpecialMark("");
                // 生成送评条码
                if (item.getDiyCode() == null || item.getDiyCode().trim().isEmpty()) {
                    String diyCode = generateDiyCode(item.getCoinType());
                    item.setDiyCode(diyCode);
                }

                // 设置创建时间（如果为空）
                if (item.getCreateTime() == null) {
                    item.setCreateTime(new java.util.Date());
                }

                // 确保数值字段不为null（使用BigDecimal类型）
                if (item.getGradeFee() == null) {
                    item.setGradeFee(new java.math.BigDecimal("0.00"));
                }
                if (item.getStandardPrice() == null) {
                    item.setStandardPrice(new java.math.BigDecimal("0.00"));
                }
                if (item.getInternationalPrice() == null) {
                    item.setInternationalPrice(new java.math.BigDecimal("0.00"));
                }
                if (item.getDiscount() == null) {
                    item.setDiscount(new java.math.BigDecimal("0.00"));
                }
                if (item.getBoxFee() == null) {
                    item.setBoxFee(new java.math.BigDecimal("0.00"));
                }
                if (item.getCoinWeight() == null) {
                    item.setCoinWeight(new java.math.BigDecimal("0.000"));
                }
                if (item.getFee() == null) {
                    item.setFee(new java.math.BigDecimal("0.00"));
                }

                // 确保字符串字段不为null
                /*if (item.getAuthenticity() == null) {
                    item.setAuthenticity("");
                }
                if (item.getInspectionNote() == null) {
                    item.setInspectionNote("");
                }*/

            }

            boolean result = saveBatch(expandedItems);

            return result;
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }
    
    /**
     * 生成送评条码
     * 格式：钱币类型前缀 + 日期(yyMM) + 连续序号(4位)
     */
    private synchronized String generateDiyCode(String coinType) {
        String prefix = "ZK"; // 默认纸币

        // 根据钱币类型确定前缀
        if (coinType != null) {
            switch (coinType) {
                case "纸币":
                    prefix = "ZK";
                    break;
                case "古钱币":
                    prefix = "GQ";
                    break;
                case "机制币":
                    prefix = "JZ";
                    break;
                case "银锭":
                    prefix = "YD";
                    break;
                default:
                    prefix = "ZK";
                    break;
            }
        }

        // 生成日期部分 (yyMM)
        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyMM"));

        // 查询当前月份该类型钱币的最大序号
        Integer maxSeq = baseMapper.getMaxSequenceNumber(prefix, dateStr);
        if (maxSeq == null) {
            maxSeq = 0;
        }

        // 生成下一个序号，确保4位数格式
        int nextSeq = maxSeq + 1;
        String seqStr = String.format("%04d", nextSeq);

        return prefix + dateStr + seqStr;
    }

    /**
     * 深拷贝钱币对象
     */
    private PjOSendformItem copyItem(PjOSendformItem original) {
        PjOSendformItem copy = new PjOSendformItem();

        // 复制所有字段（除了ID，让数据库自动生成）
        copy.setSendnum(original.getSendnum());
        copy.setSeqno(original.getSeqno());
//        copy.setCoinName(original.getCoinName());
        copy.setCoinType(original.getCoinType());
        copy.setYear(original.getYear());
        copy.setFaceValue(original.getFaceValue());
        copy.setVersion(original.getVersion());
        copy.setQuantity(original.getQuantity());
        copy.setGradeFee(original.getGradeFee());
        copy.setRemark(original.getRemark());
        copy.setDiyCode(original.getDiyCode());
        copy.setSerialNumber(original.getSerialNumber());
        copy.setSerialNumber(original.getSerialNumber());
        copy.setCoinName1(original.getCoinName1());
        copy.setCoinName2(original.getCoinName2());
        copy.setCoinName3(original.getCoinName3());
        copy.setYearInfo(original.getYearInfo());
        copy.setRegion(original.getRegion());
        copy.setTaxType(original.getTaxType());
//        copy.setFaceType(original.getFaceType());
        copy.setCoinSize(original.getCoinSize());
        copy.setCoinWeight(original.getCoinWeight());
        copy.setBoxFee(original.getBoxFee());
        copy.setUrgentFee(original.getUrgentFee());
        copy.setCatalog(original.getCatalog());
        copy.setRank(original.getRank());
        copy.setBankName(original.getBankName());
        copy.setSpecialMark(original.getSpecialMark());
        copy.setInternalNote(original.getInternalNote());
        copy.setExternalNote(original.getExternalNote());
        copy.setSpecialLabel(original.getSpecialLabel());
        copy.setAuthenticity(original.getAuthenticity());
        copy.setGradeScore(original.getGradeScore());
        copy.setScoreRemarks(original.getScoreRemarks());
        copy.setInspectionNote(original.getInspectionNote());
        copy.setCoinImages(original.getCoinImages());
        copy.setBelongName(original.getBelongName());
        copy.setWeightName(original.getWeightName());
        copy.setMaterial(original.getMaterial());
        copy.setBoxType(original.getBoxType());
        copy.setStandardPrice(original.getStandardPrice());
        copy.setInternationalPrice(original.getInternationalPrice());
        copy.setFee(original.getFee());
        copy.setDiscount(original.getDiscount());
        copy.setCreateTime(original.getCreateTime());

        return copy;
    }
}