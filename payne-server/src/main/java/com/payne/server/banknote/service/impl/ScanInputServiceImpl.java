package com.payne.server.banknote.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.payne.core.utils.AssertUtil;
import com.payne.server.banknote.dto.ScanQueryDto;
import com.payne.server.banknote.entity.PjOSendformItem;
import com.payne.server.banknote.enums.CoinTypeEnum;
import com.payne.upms.code.util.CodeCommonUtil;
import com.payne.server.banknote.mapper.PjOSendformItemMapper;
import com.payne.server.banknote.service.ScanInputService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 扫码录入Service实现类
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@Service
public class ScanInputServiceImpl implements ScanInputService {

    @Resource
    private PjOSendformItemMapper pjOSendformItemMapper;

    @Override
    public List<PjOSendformItem> queryCoinsByScan(ScanQueryDto queryDto) {
        List<PjOSendformItem> coinList = new ArrayList<>();

        // 智能识别diyCode字段中的内容（可能是送评单号或送评条码）
        String inputCode = null;
        if (StringUtils.hasText(queryDto.getDiyCode())) {
            inputCode = queryDto.getDiyCode().trim();
        } else if (StringUtils.hasText(queryDto.getSendnum())) {
            inputCode = queryDto.getSendnum().trim();
        } else if (StringUtils.hasText(queryDto.getSerialNumber())) {
            inputCode = queryDto.getSerialNumber().trim();
        }

        AssertUtil.isTrue(StringUtils.hasText(inputCode), "请提供送评单号、送评条码或钱币编号");

        // 先尝试作为送评单号查询
        LambdaQueryWrapper<PjOSendformItem> sendformWrapper = new LambdaQueryWrapper<>();
        sendformWrapper.eq(PjOSendformItem::getSendnum, inputCode)
                      .orderByAsc(PjOSendformItem::getSeqno);

        // 排除已存在的送评条码
        if (queryDto.getDiyCodes() != null && !queryDto.getDiyCodes().isEmpty()) {
            sendformWrapper.notIn(PjOSendformItem::getDiyCode, queryDto.getDiyCodes());
        }

        // 排除已存在的钱币编号
        if (queryDto.getSerialNumbers() != null && !queryDto.getSerialNumbers().isEmpty()) {
            sendformWrapper.notIn(PjOSendformItem::getSerialNumber, queryDto.getSerialNumbers());
        }

        coinList = pjOSendformItemMapper.selectList(sendformWrapper);

        // 如果按送评单号没有找到结果，则尝试作为送评条码或钱币编号查询
        if (coinList.isEmpty()) {
            LambdaQueryWrapper<PjOSendformItem> wrapper = new LambdaQueryWrapper<>();

            // 先尝试作为送评条码查询
            wrapper.eq(PjOSendformItem::getDiyCode, inputCode);
            PjOSendformItem coin = pjOSendformItemMapper.selectOne(wrapper);

            // 如果送评条码没找到，再尝试作为钱币编号查询
            if (coin == null) {
                wrapper.clear();
                wrapper.eq(PjOSendformItem::getSerialNumber, inputCode);
                coin = pjOSendformItemMapper.selectOne(wrapper);
            }

            AssertUtil.isTrue(coin != null, "未找到对应的钱币信息");

            // 根据追加类型决定查询范围
            Integer addType = queryDto.getAddType();
            if (addType != null && addType == 1) {
                // 追加该送评单所有订单
                LambdaQueryWrapper<PjOSendformItem> addSendformWrapper = new LambdaQueryWrapper<>();
                addSendformWrapper.eq(PjOSendformItem::getSendnum, coin.getSendnum());

                // 排除已存在的送评条码
                if (queryDto.getDiyCodes() != null && !queryDto.getDiyCodes().isEmpty()) {
                    addSendformWrapper.notIn(PjOSendformItem::getDiyCode, queryDto.getDiyCodes());
                }

                // 排除已存在的钱币编号
                if (queryDto.getSerialNumbers() != null && !queryDto.getSerialNumbers().isEmpty()) {
                    addSendformWrapper.notIn(PjOSendformItem::getSerialNumber, queryDto.getSerialNumbers());
                }

                coinList = pjOSendformItemMapper.selectList(addSendformWrapper);
            } else if (addType != null && addType == 2) {
                // 追加该鉴定单所有订单（可根据具体业务需求调整）
                coinList.add(coin);
            } else {
                // 仅返回当前条形码对应的钱币
                coinList.add(coin);
            }
        }

        // 根据钱币类型过滤
        if (StringUtils.hasText(queryDto.getGm())) {
            String coinType = convertGmToCoinType(queryDto.getGm());
            coinList = coinList.stream()
                    .filter(item -> Objects.equals(item.getCoinType(), coinType))
                    .collect(Collectors.toList());
        }
        return coinList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateCoins(List<PjOSendformItem> coinList) {
        if (coinList == null || coinList.isEmpty()) {
            return true;
        }

        try {
            for (PjOSendformItem coin : coinList) {
                if (coin.getId() != null) {
                    // 更新现有记录
                    LambdaUpdateWrapper<PjOSendformItem> updateWrapper = new LambdaUpdateWrapper<>();
                    updateWrapper.eq(PjOSendformItem::getId, coin.getId());

                    // 钱币名称字段
                    if (StringUtils.hasText(coin.getCoinName1())) {
                        updateWrapper.set(PjOSendformItem::getCoinName1, coin.getCoinName1());
                    }
                    if (StringUtils.hasText(coin.getCoinName2())) {
                        updateWrapper.set(PjOSendformItem::getCoinName2, coin.getCoinName2());
                    }
                    if (StringUtils.hasText(coin.getCoinName3())) {
                        updateWrapper.set(PjOSendformItem::getCoinName3, coin.getCoinName3());
                    }

                    // 版别
                    if (StringUtils.hasText(coin.getVersion())) {
                        updateWrapper.set(PjOSendformItem::getVersion, coin.getVersion());
                    }

                    // 年代
                    if (StringUtils.hasText(coin.getYearInfo())) {
                        updateWrapper.set(PjOSendformItem::getYearInfo, coin.getYearInfo());
                    }

                    // 编号
                    if (StringUtils.hasText(coin.getSerialNumber())) {
                        updateWrapper.set(PjOSendformItem::getSerialNumber, coin.getSerialNumber());
                    }

                    // 银行
                    if (StringUtils.hasText(coin.getBankName())) {
                        updateWrapper.set(PjOSendformItem::getBankName, coin.getBankName());
                    }

                    // 品相分数
                    if (StringUtils.hasText(coin.getGradeScore())) {
                        updateWrapper.set(PjOSendformItem::getGradeScore, coin.getGradeScore());
                    }

                    // ★/EPQ/NET（特殊标签）
                    if (StringUtils.hasText(coin.getSpecialLabel())) {
                        updateWrapper.set(PjOSendformItem::getSpecialLabel, coin.getSpecialLabel());
                    }

                    // 星级（等级）
                    if (StringUtils.hasText(coin.getRank())) {
                        updateWrapper.set(PjOSendformItem::getRank, coin.getRank());
                    }

                    // 真伪
                    if (StringUtils.hasText(coin.getAuthenticity())) {
                        updateWrapper.set(PjOSendformItem::getAuthenticity, coin.getAuthenticity());
                    }

                    // 地区
                    if (StringUtils.hasText(coin.getRegion())) {
                        updateWrapper.set(PjOSendformItem::getRegion, coin.getRegion());
                    }

                    // 目录
                    if (StringUtils.hasText(coin.getCatalog())) {
                        updateWrapper.set(PjOSendformItem::getCatalog, coin.getCatalog());
                    }

                    // 钱币备注
                    if (StringUtils.hasText(coin.getRemark())) {
                        updateWrapper.set(PjOSendformItem::getRemark, coin.getRemark());
                    }

                    // 对内备注
                    if (StringUtils.hasText(coin.getInternalNote())) {
                        updateWrapper.set(PjOSendformItem::getInternalNote, coin.getInternalNote());
                    }

                    // 对外备注
                    if (StringUtils.hasText(coin.getExternalNote())) {
                        updateWrapper.set(PjOSendformItem::getExternalNote, coin.getExternalNote());
                    }

                    // 其他字段（保留原有逻辑）
                    if (StringUtils.hasText(coin.getBoxType())) {
                        updateWrapper.set(PjOSendformItem::getBoxType, coin.getBoxType());
                    }
                    if (StringUtils.hasText(coin.getInspectionNote())) {
                        updateWrapper.set(PjOSendformItem::getInspectionNote, coin.getInspectionNote());
                    }
                    if (StringUtils.hasText(coin.getSpecialMark())) {
                        updateWrapper.set(PjOSendformItem::getSpecialMark, coin.getSpecialMark());
                    }
                    if (StringUtils.hasText(coin.getScoreRemarks())) {
                        updateWrapper.set(PjOSendformItem::getScoreRemarks, coin.getScoreRemarks());
                    }

                    // 数值字段处理
                    if (coin.getGradeFee() != null) {
                        updateWrapper.set(PjOSendformItem::getGradeFee, coin.getGradeFee());
                    }
                    if (coin.getStandardPrice() != null) {
                        updateWrapper.set(PjOSendformItem::getStandardPrice, coin.getStandardPrice());
                    }
                    if (coin.getInternationalPrice() != null) {
                        updateWrapper.set(PjOSendformItem::getInternationalPrice, coin.getInternationalPrice());
                    }
                    if (coin.getFee() != null) {
                        updateWrapper.set(PjOSendformItem::getFee, coin.getFee());
                    }
                    if (coin.getDiscount() != null) {
                        updateWrapper.set(PjOSendformItem::getDiscount, coin.getDiscount());
                    }
                    if (coin.getCoinWeight() != null) {
                        updateWrapper.set(PjOSendformItem::getCoinWeight, coin.getCoinWeight());
                    }
                    if (coin.getBoxFee() != null) {
                        updateWrapper.set(PjOSendformItem::getBoxFee, coin.getBoxFee());
                    }

                    pjOSendformItemMapper.update(null, updateWrapper);
                }
            }
            return true;
        } catch (Exception e) {
            log.error("批量更新钱币信息失败", e);
            throw new RuntimeException("批量更新失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<PjOSendformItem> getCoinsBySendnum(String sendnum) {
        LambdaQueryWrapper<PjOSendformItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PjOSendformItem::getSendnum, sendnum)
               .orderByAsc(PjOSendformItem::getSeqno);
        return pjOSendformItemMapper.selectList(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeCoins(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            return true;
        }
        
        try {
            int deleted = pjOSendformItemMapper.deleteBatchIds(ids);
            return deleted > 0;
        } catch (Exception e) {
            throw new RuntimeException("删除失败", e);
        }
    }

    @Override
    public List<Map<String, Object>> getCoinTypes() {
        // 使用动态码表数据替代硬编码
        return CodeCommonUtil.getCoinTypes();
    }

    @Override
    public List<Map<String, Object>> getResultOptions() {
        List<Map<String, Object>> result = new ArrayList<>();
        
        Map<String, Object> genuine = new HashMap<>();
        genuine.put("value", "真");
        genuine.put("label", "真");
        genuine.put("color", "success");
        result.add(genuine);
        
        Map<String, Object> fake = new HashMap<>();
        fake.put("value", "假");
        fake.put("label", "假");
        fake.put("color", "danger");
        result.add(fake);
        
        Map<String, Object> uncertain = new HashMap<>();
        uncertain.put("value", "不确定");
        uncertain.put("label", "不确定");
        uncertain.put("color", "warning");
        result.add(uncertain);
        
        return result;
    }

    @Override
    public List<Map<String, Object>> getBoxTypes() {
        List<Map<String, Object>> result = new ArrayList<>();
        
        String[] boxTypes = {
            "标准盒", "加厚盒", "超厚盒", "特制盒", "不装盒", "自定义"
        };
        
        for (String boxType : boxTypes) {
            Map<String, Object> box = new HashMap<>();
            box.put("value", boxType);
            box.put("label", boxType);
            result.add(box);
        }
        
        return result;
    }

    @Override
    public PjOSendformItem getCoinDetailByDiyCode(String diyCode) {
        LambdaQueryWrapper<PjOSendformItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PjOSendformItem::getDiyCode, diyCode);
        return pjOSendformItemMapper.selectOne(wrapper);
    }

    @Override
    public PjOSendformItem getCoinDetailByNumber(String nummber) {
        LambdaQueryWrapper<PjOSendformItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PjOSendformItem::getSerialNumber, nummber);
        return pjOSendformItemMapper.selectOne(wrapper);
    }

    @Override
    public Map<String, Object> validateDiyCode(String diyCode) {
        Map<String, Object> result = new HashMap<>();

        LambdaQueryWrapper<PjOSendformItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PjOSendformItem::getDiyCode, diyCode);

        PjOSendformItem coin = pjOSendformItemMapper.selectOne(wrapper);

        if (coin != null) {
            result.put("exists", true);
            result.put("sendnum", coin.getSendnum());
            result.put("coinType", coin.getCoinType());
            result.put("coinName", coin.getCoinName1());
            result.put("diyCode", coin.getDiyCode());
            result.put("serialNumber", coin.getSerialNumber());
        } else {
            result.put("exists", false);
        }

        return result;
    }

    @Override
    public Map<String, Object> validateNumber(String nummber) {
        Map<String, Object> result = new HashMap<>();

        LambdaQueryWrapper<PjOSendformItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PjOSendformItem::getSerialNumber, nummber);

        PjOSendformItem coin = pjOSendformItemMapper.selectOne(wrapper);

        if (coin != null) {
            result.put("exists", true);
            result.put("sendnum", coin.getSendnum());
            result.put("coinType", coin.getCoinType());
            result.put("coinName", coin.getCoinName1());
            result.put("diyCode", coin.getDiyCode());
            result.put("serialNumber", coin.getSerialNumber());
        } else {
            result.put("exists", false);
        }

        return result;
    }

    @Override
    public void exportScanData(Map<String, Object> params, HttpServletResponse response) {
        try {
            // 构建查询条件
            LambdaQueryWrapper<PjOSendformItem> wrapper = new LambdaQueryWrapper<>();
            
            // 根据参数添加查询条件
            if (params.containsKey("sendnum")) {
                wrapper.eq(PjOSendformItem::getSendnum, params.get("sendnum"));
            }
            if (params.containsKey("coinType")) {
                wrapper.eq(PjOSendformItem::getCoinType, params.get("coinType"));
            }
            if (params.containsKey("startDate")) {
                wrapper.ge(PjOSendformItem::getCreateTime, params.get("startDate"));
            }
            if (params.containsKey("endDate")) {
                wrapper.le(PjOSendformItem::getCreateTime, params.get("endDate"));
            }
            
            wrapper.orderByAsc(PjOSendformItem::getSendnum, PjOSendformItem::getSeqno);
            
            List<PjOSendformItem> coinList = pjOSendformItemMapper.selectList(wrapper);
            
            // 构建CSV内容
            StringBuilder csvContent = new StringBuilder();
            csvContent.append("送评单号,条形码,钱币名称,钱币类型,年份,面值,数量,真伪,等级,地区,盒子类型,对内备注,对外备注\n");
            
            for (PjOSendformItem coin : coinList) {
                csvContent.append(quote(coin.getSendnum())).append(",")
                         .append(quote(coin.getSerialNumber())).append(",")
                         .append(quote(coin.getCoinName1())).append(",")
                         .append(quote(coin.getCoinType())).append(",")
                         .append(quote(coin.getYear())).append(",")
                         .append(quote(coin.getFaceValue())).append(",")
                         .append(quote(coin.getQuantity())).append(",")
                         .append(quote(coin.getAuthenticity())).append(",")
                         .append(quote(coin.getGradeScore())).append(",")
                         .append(quote(coin.getRegion())).append(",")
                         .append(quote(coin.getBoxType())).append(",")
                         .append(quote(coin.getInternalNote())).append(",")
                         .append(quote(coin.getExternalNote())).append("\n");
            }
            
            // 设置响应头
            response.setContentType("text/csv;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=\"scan_data_" + 
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".csv\"");
            
            // 写入BOM以支持Excel正确显示中文
            OutputStream outputStream = response.getOutputStream();
            outputStream.write(new byte[]{(byte) 0xEF, (byte) 0xBB, (byte) 0xBF});
            
            // 写入数据
            outputStream.write(csvContent.toString().getBytes(StandardCharsets.UTF_8));
            outputStream.flush();
            outputStream.close();
            
        } catch (IOException e) {
            throw new RuntimeException("导出失败", e);
        }
    }

    @Override
    public List<PjOSendformItem> findCoinsBySameSendformByDiyCode(String diyCode) {
        // 先根据送评条码找到对应的钱币
        LambdaQueryWrapper<PjOSendformItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PjOSendformItem::getDiyCode, diyCode);

        PjOSendformItem coin = pjOSendformItemMapper.selectOne(wrapper);

        if (coin == null) {
            return new ArrayList<>();
        }

        // 根据送评单号查找同一送评单下的所有钱币
        LambdaQueryWrapper<PjOSendformItem> sendformWrapper = new LambdaQueryWrapper<>();
        sendformWrapper.eq(PjOSendformItem::getSendnum, coin.getSendnum())
                      .orderByAsc(PjOSendformItem::getSeqno);

        return pjOSendformItemMapper.selectList(sendformWrapper);
    }

    @Override
    public List<PjOSendformItem> findCoinsBySameSendformByNumber(String nummber) {
        // 先根据钱币编号找到对应的钱币
        LambdaQueryWrapper<PjOSendformItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PjOSendformItem::getSerialNumber, nummber);

        PjOSendformItem coin = pjOSendformItemMapper.selectOne(wrapper);

        if (coin == null) {
            return new ArrayList<>();
        }

        // 根据送评单号查找同一送评单下的所有钱币
        LambdaQueryWrapper<PjOSendformItem> sendformWrapper = new LambdaQueryWrapper<>();
        sendformWrapper.eq(PjOSendformItem::getSendnum, coin.getSendnum())
                      .orderByAsc(PjOSendformItem::getSeqno);

        return pjOSendformItemMapper.selectList(sendformWrapper);
    }

    @Override
    @Deprecated
    public List<PjOSendformItem> findCoinsBySameSendform(String nummber) {
        // 兼容方法，直接调用新方法
        return findCoinsBySameSendformByNumber(nummber);
    }

    @Override
    @Deprecated
    public PjOSendformItem getCoinDetail(String nummber) {
        // 兼容方法，直接调用新方法
        return getCoinDetailByNumber(nummber);
    }

    @Override
    @Deprecated
    public Map<String, Object> validateBarcode(String nummber) {
        // 兼容方法，直接调用新方法
        return validateNumber(nummber);
    }

    @Override
    public List<String> generateBarcodes(String coinType, Integer count) {
        List<String> barcodes = new ArrayList<>();

        String prefix = getPrefix(coinType);
        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyMM"));

        // 查询当前月份该类型钱币的最大序号
        Integer maxSeq = getMaxSequenceNumber(prefix, dateStr);
        if (maxSeq == null) {
            maxSeq = 0;
        }

        // 生成连续的条形码
        for (int i = 0; i < count; i++) {
            int nextSeq = maxSeq + i + 1;
            String seqStr = String.format("%04d", nextSeq);
            String barcode = prefix + dateStr + seqStr;
            barcodes.add(barcode);
        }

        return barcodes;
    }

    /**
     * 将gm代码转换为钱币类型 - 使用动态码表
     * gm代码(01,02,03,04) -> 钱币类型代码(banknote,ancientCoin,machineCoin,silverIngot)
     */
    private String convertGmToCoinType(String gm) {
        String coinTypeCode = null;
        switch (gm) {
            case "01":
                coinTypeCode = "banknote";
                break;
            case "02":
                coinTypeCode = "ancientCoin";
                break;
            case "03":
                coinTypeCode = "machineCoin";
                break;
            case "04":
                coinTypeCode = "silverIngot";
                break;
            default:
                return null;
        }
        return CodeCommonUtil.getCoinTypeNameByCode(coinTypeCode);
    }

    /**
     * 根据钱币类型获取前缀
     */
    private String getPrefix(String coinType) {
        if (coinType == null) {
            return "ZK";
        }

        switch (coinType) {
            case "纸币":
                return "ZK";
            case "古钱币":
                return "GQ";
            case "机制币":
                return "JZ";
            case "银锭":
                return "YD";
            default:
                return "ZK";
        }
    }

    /**
     * 查询指定前缀和日期的最大序号
     */
    private Integer getMaxSequenceNumber(String prefix, String dateStr) {
        LambdaQueryWrapper<PjOSendformItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.likeRight(PjOSendformItem::getSerialNumber, prefix + dateStr);
        List<PjOSendformItem> existingCoins = pjOSendformItemMapper.selectList(wrapper);

        int maxSeq = 0;
        for (PjOSendformItem coin : existingCoins) {
            if (coin.getSerialNumber() != null && coin.getSerialNumber().startsWith(prefix + dateStr)) {
                try {
                    String seqStr = coin.getSerialNumber().substring((prefix + dateStr).length());
                    if (seqStr.length() == 4) { // 确保是4位序号
                        int seq = Integer.parseInt(seqStr);
                        maxSeq = Math.max(maxSeq, seq);
                    }
                } catch (NumberFormatException e) {
                    // 忽略无法解析的序号
                }
            }
        }

        return maxSeq;
    }

    /**
     * CSV字段添加引号
     */
    private String quote(Object value) {
        if (value == null) {
            return "\"\"";
        }
        String str = value.toString();
        return "\"" + str.replace("\"", "\"\"") + "\"";
    }
} 