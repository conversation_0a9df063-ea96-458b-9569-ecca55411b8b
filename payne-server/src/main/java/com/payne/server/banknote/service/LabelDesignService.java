package com.payne.server.banknote.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.payne.server.banknote.entity.FieldDefinition;
import com.payne.server.banknote.entity.LabelTemplate;
import com.payne.server.banknote.dto.LabelTemplateDto;

import java.util.List;
import java.util.Map;

/**
 * 标签设计服务接口
 *
 * <AUTHOR>
 * @since 2025-01-13
 */
public interface LabelDesignService extends IService<LabelTemplate> {
    
    /**
     * 获取可用字段列表（基于PjOSendformItem实体）
     */
    List<FieldDefinition> getAvailableFields();
    
    /**
     * 按分类获取字段列表
     */
    Map<String, List<FieldDefinition>> getFieldsByCategory();
    
    /**
     * 保存标签模板
     */
    LabelTemplate saveTemplate(LabelTemplateDto templateDto);
    
    /**
     * 获取模板列表
     */
    List<LabelTemplate> getTemplateList();
    
    /**
     * 获取默认模板
     */
    LabelTemplate getDefaultTemplate();

    /**
     * 设置默认模板
     */
    LabelTemplate setDefaultTemplate(String id);

}