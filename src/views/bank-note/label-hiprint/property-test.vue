<template>
  <div class="property-test-page">
    <h2>属性面板测试页面</h2>
    
    <div class="test-container">
      <div class="left-panel">
        <h3>测试控制</h3>
        <div class="test-buttons">
          <el-button @click="selectTestElement1" type="primary">
            选中文本元素
          </el-button>
          <el-button @click="selectTestElement2" type="success">
            选中图片元素
          </el-button>
          <el-button @click="clearSelection" type="warning">
            清空选择
          </el-button>
        </div>
        
        <div class="current-selection">
          <h4>当前选中元素:</h4>
          <pre>{{ JSON.stringify(selectedElement, null, 2) }}</pre>
        </div>
      </div>
      
      <div class="right-panel">
        <h3>属性面板</h3>
        <SimplePropertyPanel
          :selected-element="selectedElement"
          @element-update="handleElementUpdate"
          @element-copy="handleElementCopy"
          @element-delete="handleElementDelete"
        />
      </div>
    </div>
    
    <div class="logs">
      <h3>操作日志</h3>
      <div class="log-content">
        <div v-for="(log, index) in logs" :key="index" class="log-item">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { EleMessage } from 'ele-admin-plus/es';
import SimplePropertyPanel from './components/SimplePropertyPanel.vue';

const selectedElement = ref(null);
const logs = ref([]);

// 添加日志
const addLog = (message) => {
  logs.value.unshift({
    time: new Date().toLocaleTimeString(),
    message
  });
  
  // 只保留最近20条日志
  if (logs.value.length > 20) {
    logs.value = logs.value.slice(0, 20);
  }
};

// 选中测试元素1（文本）
const selectTestElement1 = () => {
  const element = {
    tid: 'test_text_' + Date.now(),
    type: 'text',
    title: '测试文本元素',
    options: {
      left: 50,
      top: 50,
      width: 200,
      height: 40,
      text: '这是一个测试文本元素',
      fontSize: 16,
      fontFamily: 'Microsoft YaHei',
      color: '#333333',
      backgroundColor: '#ffffff',
      borderWidth: 1,
      borderColor: '#cccccc',
      borderStyle: 'solid',
      textAlign: 'left'
    },
    _isTemp: true
  };
  
  selectedElement.value = element;
  addLog('选中了文本元素: ' + element.title);
  EleMessage.success('已选中文本元素');
};

// 选中测试元素2（图片）
const selectTestElement2 = () => {
  const element = {
    tid: 'test_image_' + Date.now(),
    type: 'image',
    title: '测试图片元素',
    options: {
      left: 100,
      top: 100,
      width: 150,
      height: 100,
      src: 'https://via.placeholder.com/150x100',
      fit: 'contain',
      backgroundColor: '#f0f0f0',
      borderWidth: 2,
      borderColor: '#409eff',
      borderStyle: 'solid'
    },
    _isTemp: true
  };
  
  selectedElement.value = element;
  addLog('选中了图片元素: ' + element.title);
  EleMessage.success('已选中图片元素');
};

// 清空选择
const clearSelection = () => {
  selectedElement.value = null;
  addLog('清空了元素选择');
  EleMessage.info('已清空选择');
};

// 处理元素更新
const handleElementUpdate = ({ element, property, value }) => {
  addLog(`更新属性: ${property} = ${value}`);
  console.log('元素属性更新:', { element, property, value });
  EleMessage.success(`属性 ${property} 已更新为 ${value}`);
};

// 处理元素复制
const handleElementCopy = (element) => {
  addLog(`复制元素: ${element.title || element.tid}`);
  console.log('复制元素:', element);
  EleMessage.success('元素已复制');
};

// 处理元素删除
const handleElementDelete = (element) => {
  addLog(`删除元素: ${element.title || element.tid}`);
  console.log('删除元素:', element);
  selectedElement.value = null;
  EleMessage.success('元素已删除');
};
</script>

<style scoped>
.property-test-page {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.test-container {
  display: flex;
  gap: 20px;
  margin: 20px 0;
  height: 600px;
}

.left-panel {
  flex: 1;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.right-panel {
  width: 320px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.test-buttons {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.current-selection {
  margin-top: 20px;
}

.current-selection h4 {
  margin-bottom: 10px;
  color: #303133;
}

.current-selection pre {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e9ecef;
}

.logs {
  margin-top: 20px;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.log-content {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 12px;
  background: #f8f9fa;
}

.log-item {
  margin-bottom: 8px;
  font-size: 12px;
  font-family: 'Courier New', monospace;
}

.log-time {
  color: #909399;
  margin-right: 8px;
}

.log-message {
  color: #303133;
}
</style>
