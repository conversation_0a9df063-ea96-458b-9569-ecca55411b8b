<template>
  <div class="hiprint-property-panel">
    <div class="panel-header">
      <span>属性设置</span>
    </div>

    <div v-if="selectedElement" class="panel-content">
      <!-- 基本信息 -->
      <div class="property-section">
        <h4 class="section-title">基本信息</h4>
        <el-form :model="elementProps" label-width="80px" size="small">
          <el-form-item label="元素类型">
            <el-input :model-value="getElementTypeName()" disabled />
          </el-form-item>
          <el-form-item label="标题">
            <el-input
              v-model="elementProps.title"
              @change="updateElementProperty('title', elementProps.title)"
              placeholder="请输入标题"
            />
          </el-form-item>
        </el-form>
      </div>

      <!-- 位置和尺寸 -->
      <div class="property-section">
        <h4 class="section-title">位置和尺寸</h4>
        <el-form :model="elementProps" label-width="80px" size="small">
          <el-form-item label="位置">
            <div class="position-inputs">
              <el-input-number
                v-model="elementProps.left"
                :min="0"
                :step="1"
                :precision="0"
                @change="updateElementProperty('left', elementProps.left)"
                style="width: 80px"
              />
              <span class="input-separator">×</span>
              <el-input-number
                v-model="elementProps.top"
                :min="0"
                :step="1"
                :precision="0"
                @change="updateElementProperty('top', elementProps.top)"
                style="width: 80px"
              />
              <span class="unit">px</span>
            </div>
          </el-form-item>
          <el-form-item label="尺寸">
            <div class="size-inputs">
              <el-input-number
                v-model="elementProps.width"
                :min="10"
                :step="1"
                :precision="0"
                @change="updateElementProperty('width', elementProps.width)"
                style="width: 80px"
              />
              <span class="input-separator">×</span>
              <el-input-number
                v-model="elementProps.height"
                :min="10"
                :step="1"
                :precision="0"
                @change="updateElementProperty('height', elementProps.height)"
                style="width: 80px"
              />
              <span class="unit">px</span>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <!-- 文本属性 (仅文本类元素显示) -->
      <div v-if="isTextElement()" class="property-section">
        <h4 class="section-title">文本属性</h4>
        <el-form :model="elementProps" label-width="80px" size="small">
          <el-form-item label="文本内容">
            <el-input
              v-model="elementProps.text"
              @change="updateElementProperty('text', elementProps.text)"
              placeholder="请输入文本内容"
              type="textarea"
              :rows="2"
            />
          </el-form-item>
          <el-form-item label="字体大小">
            <el-input-number
              v-model="elementProps.fontSize"
              :min="8"
              :max="72"
              :step="1"
              @change="updateElementProperty('fontSize', elementProps.fontSize)"
              style="width: 100px"
            />
            <span class="unit">px</span>
          </el-form-item>
          <el-form-item label="字体">
            <el-select
              v-model="elementProps.fontFamily"
              @change="updateElementProperty('fontFamily', elementProps.fontFamily)"
              style="width: 100%"
            >
              <el-option label="微软雅黑" value="Microsoft YaHei" />
              <el-option label="宋体" value="SimSun" />
              <el-option label="黑体" value="SimHei" />
              <el-option label="楷体" value="KaiTi" />
              <el-option label="Arial" value="Arial" />
              <el-option label="Times New Roman" value="Times New Roman" />
            </el-select>
          </el-form-item>
          <el-form-item label="文字颜色">
            <el-color-picker
              v-model="elementProps.color"
              @change="updateElementProperty('color', elementProps.color)"
              show-alpha
            />
          </el-form-item>
          <el-form-item label="对齐方式">
            <el-select
              v-model="elementProps.textAlign"
              @change="updateElementProperty('textAlign', elementProps.textAlign)"
              style="width: 100%"
            >
              <el-option label="左对齐" value="left" />
              <el-option label="居中" value="center" />
              <el-option label="右对齐" value="right" />
            </el-select>
          </el-form-item>
          <el-form-item label="字体样式">
            <div class="font-style-controls">
              <el-checkbox
                v-model="elementProps.fontWeight"
                true-label="bold"
                false-label="normal"
                @change="updateElementProperty('fontWeight', elementProps.fontWeight)"
              >
                加粗
              </el-checkbox>
              <el-checkbox
                v-model="elementProps.fontStyle"
                true-label="italic"
                false-label="normal"
                @change="updateElementProperty('fontStyle', elementProps.fontStyle)"
              >
                斜体
              </el-checkbox>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <!-- 图片属性 (仅图片元素显示) -->
      <div v-if="isImageElement()" class="property-section">
        <h4 class="section-title">图片属性</h4>
        <el-form :model="elementProps" label-width="80px" size="small">
          <el-form-item label="图片地址">
            <el-input
              v-model="elementProps.src"
              @change="updateElementProperty('src', elementProps.src)"
              placeholder="请输入图片URL"
            />
          </el-form-item>
          <el-form-item label="适应方式">
            <el-select
              v-model="elementProps.fit"
              @change="updateElementProperty('fit', elementProps.fit)"
              style="width: 100%"
            >
              <el-option label="包含" value="contain" />
              <el-option label="覆盖" value="cover" />
              <el-option label="填充" value="fill" />
              <el-option label="缩放" value="scale-down" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <!-- 条形码属性 (仅条形码元素显示) -->
      <div v-if="isBarcodeElement()" class="property-section">
        <h4 class="section-title">条形码属性</h4>
        <el-form :model="elementProps" label-width="80px" size="small">
          <el-form-item label="条码内容">
            <el-input
              v-model="elementProps.text"
              @change="updateElementProperty('text', elementProps.text)"
              placeholder="请输入条码内容"
            />
          </el-form-item>
          <el-form-item label="条码类型">
            <el-select
              v-model="elementProps.code"
              @change="updateElementProperty('code', elementProps.code)"
              style="width: 100%"
            >
              <el-option label="CODE128" value="CODE128" />
              <el-option label="CODE39" value="CODE39" />
              <el-option label="EAN13" value="EAN13" />
              <el-option label="EAN8" value="EAN8" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <!-- 二维码属性 (仅二维码元素显示) -->
      <div v-if="isQRCodeElement()" class="property-section">
        <h4 class="section-title">二维码属性</h4>
        <el-form :model="elementProps" label-width="80px" size="small">
          <el-form-item label="二维码内容">
            <el-input
              v-model="elementProps.text"
              @change="updateElementProperty('text', elementProps.text)"
              placeholder="请输入二维码内容"
              type="textarea"
              :rows="2"
            />
          </el-form-item>
        </el-form>
      </div>

      <!-- 样式设置 -->
      <div class="property-section">
        <h4 class="section-title">样式设置</h4>
        <el-form :model="elementProps" label-width="80px" size="small">
          <el-form-item label="背景颜色">
            <el-color-picker
              v-model="elementProps.backgroundColor"
              @change="updateElementProperty('backgroundColor', elementProps.backgroundColor)"
              show-alpha
            />
          </el-form-item>
          <el-form-item label="边框宽度">
            <el-input-number
              v-model="elementProps.borderWidth"
              :min="0"
              :max="10"
              :step="1"
              @change="updateElementProperty('borderWidth', elementProps.borderWidth)"
              style="width: 100px"
            />
            <span class="unit">px</span>
          </el-form-item>
          <el-form-item label="边框颜色">
            <el-color-picker
              v-model="elementProps.borderColor"
              @change="updateElementProperty('borderColor', elementProps.borderColor)"
              show-alpha
            />
          </el-form-item>
          <el-form-item label="边框样式">
            <el-select
              v-model="elementProps.borderStyle"
              @change="updateElementProperty('borderStyle', elementProps.borderStyle)"
              style="width: 100%"
            >
              <el-option label="实线" value="solid" />
              <el-option label="虚线" value="dashed" />
              <el-option label="点线" value="dotted" />
              <el-option label="无边框" value="none" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <!-- 操作按钮 -->
      <div class="property-section">
        <h4 class="section-title">操作</h4>
        <div class="action-buttons">
          <el-button size="small" @click="copyElement">
            <el-icon><DocumentCopy /></el-icon>
            复制
          </el-button>
          <el-button size="small" type="danger" @click="deleteElement">
            <el-icon><Delete /></el-icon>
            删除
          </el-button>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <el-icon size="48" color="#c0c4cc">
        <Select />
      </el-icon>
      <p>请选择一个元素来编辑属性</p>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue';
import { DocumentCopy, Delete, Select } from '@element-plus/icons-vue';
import { EleMessage } from 'ele-admin-plus/es';

const props = defineProps({
  selectedElement: {
    type: Object,
    default: null
  }
});

const emit = defineEmits(['element-update', 'element-copy', 'element-delete']);

// 元素属性
const elementProps = reactive({
  title: '',
  left: 0,
  top: 0,
  width: 100,
  height: 30,
  text: '',
  fontSize: 14,
  fontFamily: 'Microsoft YaHei',
  color: '#000000',
  textAlign: 'left',
  fontWeight: 'normal',
  fontStyle: 'normal',
  backgroundColor: '#ffffff',
  borderWidth: 1,
  borderColor: '#000000',
  borderStyle: 'solid',
  src: '',
  fit: 'contain',
  code: 'CODE128'
});

// 监听选中元素变化
watch(() => props.selectedElement, (newElement) => {
  if (newElement && newElement.options) {
    // 更新属性值
    Object.keys(elementProps).forEach(key => {
      if (newElement.options[key] !== undefined) {
        elementProps[key] = newElement.options[key];
      }
    });
  }
}, { immediate: true, deep: true });

// 获取元素类型名称
const getElementTypeName = () => {
  if (!props.selectedElement) return '';
  
  const typeMap = {
    'text': '文本',
    'image': '图片',
    'barcode': '条形码',
    'qrcode': '二维码',
    'hline': '线条',
    'rect': '矩形'
  };
  
  return typeMap[props.selectedElement.type] || '未知类型';
};

// 判断元素类型
const isTextElement = () => {
  return props.selectedElement && 
    (props.selectedElement.type === 'text' || 
     (props.selectedElement.options && props.selectedElement.options.textType === undefined));
};

const isImageElement = () => {
  return props.selectedElement && props.selectedElement.type === 'image';
};

const isBarcodeElement = () => {
  return props.selectedElement && 
    (props.selectedElement.type === 'text' && 
     props.selectedElement.options && 
     props.selectedElement.options.textType === 'barcode');
};

const isQRCodeElement = () => {
  return props.selectedElement && 
    (props.selectedElement.type === 'text' && 
     props.selectedElement.options && 
     props.selectedElement.options.textType === 'qrcode');
};

// 更新元素属性
const updateElementProperty = (property, value) => {
  if (!props.selectedElement) return;
  
  emit('element-update', {
    element: props.selectedElement,
    property,
    value
  });
};

// 复制元素
const copyElement = () => {
  if (!props.selectedElement) return;
  emit('element-copy', props.selectedElement);
  EleMessage.success('元素已复制');
};

// 删除元素
const deleteElement = () => {
  if (!props.selectedElement) return;
  emit('element-delete', props.selectedElement);
  EleMessage.success('元素已删除');
};
</script>

<style scoped>
.hiprint-property-panel {
  width: 320px;
  background: white;
  border-left: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.panel-header {
  height: 50px;
  padding: 0 16px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  font-weight: 600;
  background: #f8f9fa;
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.property-section {
  margin-bottom: 24px;
}

.section-title {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 8px;
}

.position-inputs,
.size-inputs {
  display: flex;
  align-items: center;
  gap: 8px;
}

.input-separator {
  color: #909399;
  font-weight: bold;
}

.unit {
  color: #909399;
  font-size: 12px;
}

.font-style-controls {
  display: flex;
  gap: 16px;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
  padding: 40px 20px;
}

.empty-state p {
  margin-top: 16px;
  font-size: 14px;
}
</style>
