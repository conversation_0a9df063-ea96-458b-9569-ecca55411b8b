<template>
  <div class="simple-property-panel">
    <div class="panel-header">
      <span>属性设置</span>
    </div>

    <div v-if="selectedElement" class="panel-content">
      <div class="debug-info">
        <h4>调试信息</h4>
        <p><strong>元素类型:</strong> {{ selectedElement.type || '未知' }}</p>
        <p><strong>元素ID:</strong> {{ selectedElement.tid || selectedElement.id || '无' }}</p>
        <p><strong>是否临时:</strong> {{ selectedElement._isTemp ? '是' : '否' }}</p>
      </div>

      <!-- 基本信息 -->
      <div class="property-section">
        <h4 class="section-title">基本信息</h4>
        <div class="property-item">
          <label>标题:</label>
          <el-input
            v-model="elementProps.title"
            @change="updateProperty('title', elementProps.title)"
            size="small"
            placeholder="请输入标题"
          />
        </div>
      </div>

      <!-- 位置和尺寸 -->
      <div class="property-section">
        <h4 class="section-title">位置和尺寸</h4>
        <div class="property-item">
          <label>X位置:</label>
          <el-input-number
            v-model="elementProps.left"
            @change="updateProperty('left', elementProps.left)"
            size="small"
            :min="0"
            style="width: 100%"
          />
        </div>
        <div class="property-item">
          <label>Y位置:</label>
          <el-input-number
            v-model="elementProps.top"
            @change="updateProperty('top', elementProps.top)"
            size="small"
            :min="0"
            style="width: 100%"
          />
        </div>
        <div class="property-item">
          <label>宽度:</label>
          <el-input-number
            v-model="elementProps.width"
            @change="updateProperty('width', elementProps.width)"
            size="small"
            :min="10"
            style="width: 100%"
          />
        </div>
        <div class="property-item">
          <label>高度:</label>
          <el-input-number
            v-model="elementProps.height"
            @change="updateProperty('height', elementProps.height)"
            size="small"
            :min="10"
            style="width: 100%"
          />
        </div>
      </div>

      <!-- 文本属性 -->
      <div v-if="isTextElement" class="property-section">
        <h4 class="section-title">文本属性</h4>
        <div class="property-item">
          <label>文本内容:</label>
          <el-input
            v-model="elementProps.text"
            @change="updateProperty('text', elementProps.text)"
            size="small"
            type="textarea"
            :rows="2"
            placeholder="请输入文本内容"
          />
        </div>
        <div class="property-item">
          <label>字体大小:</label>
          <el-input-number
            v-model="elementProps.fontSize"
            @change="updateProperty('fontSize', elementProps.fontSize)"
            size="small"
            :min="8"
            :max="72"
            style="width: 100%"
          />
        </div>
        <div class="property-item">
          <label>字体颜色:</label>
          <el-color-picker
            v-model="elementProps.color"
            @change="updateProperty('color', elementProps.color)"
            size="small"
          />
        </div>
        <div class="property-item">
          <label>背景颜色:</label>
          <el-color-picker
            v-model="elementProps.backgroundColor"
            @change="updateProperty('backgroundColor', elementProps.backgroundColor)"
            size="small"
            show-alpha
          />
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="property-section">
        <h4 class="section-title">操作</h4>
        <div class="action-buttons">
          <el-button size="small" @click="copyElement">复制</el-button>
          <el-button size="small" type="danger" @click="deleteElement">删除</el-button>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <el-icon size="48" color="#c0c4cc">
        <Select />
      </el-icon>
      <p>请选择一个元素来编辑属性</p>
      <p class="hint">点击设计区域中的元素</p>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue';
import { Select } from '@element-plus/icons-vue';
import { EleMessage } from 'ele-admin-plus/es';

const props = defineProps({
  selectedElement: {
    type: Object,
    default: null
  }
});

const emit = defineEmits(['element-update', 'element-copy', 'element-delete']);

// 元素属性
const elementProps = reactive({
  title: '',
  left: 0,
  top: 0,
  width: 100,
  height: 30,
  text: '',
  fontSize: 14,
  color: '#000000',
  backgroundColor: '#ffffff'
});

// 是否为文本元素
const isTextElement = computed(() => {
  return !props.selectedElement || 
         props.selectedElement.type === 'text' || 
         !props.selectedElement.type;
});

// 监听选中元素变化
watch(() => props.selectedElement, (newElement) => {
  console.log('属性面板接收到新元素:', newElement);
  
  if (newElement && newElement.options) {
    // 更新属性值
    elementProps.title = newElement.title || '';
    elementProps.left = newElement.options.left || 0;
    elementProps.top = newElement.options.top || 0;
    elementProps.width = newElement.options.width || 100;
    elementProps.height = newElement.options.height || 30;
    elementProps.text = newElement.options.text || '';
    elementProps.fontSize = newElement.options.fontSize || 14;
    elementProps.color = newElement.options.color || '#000000';
    elementProps.backgroundColor = newElement.options.backgroundColor || '#ffffff';
  } else {
    // 重置属性
    elementProps.title = '';
    elementProps.left = 0;
    elementProps.top = 0;
    elementProps.width = 100;
    elementProps.height = 30;
    elementProps.text = '';
    elementProps.fontSize = 14;
    elementProps.color = '#000000';
    elementProps.backgroundColor = '#ffffff';
  }
}, { immediate: true, deep: true });

// 更新属性
const updateProperty = (property, value) => {
  console.log('更新属性:', property, value);
  
  if (!props.selectedElement) {
    EleMessage.error('没有选中的元素');
    return;
  }
  
  emit('element-update', {
    element: props.selectedElement,
    property,
    value
  });
};

// 复制元素
const copyElement = () => {
  if (!props.selectedElement) return;
  emit('element-copy', props.selectedElement);
};

// 删除元素
const deleteElement = () => {
  if (!props.selectedElement) return;
  emit('element-delete', props.selectedElement);
};
</script>

<style scoped>
.simple-property-panel {
  width: 320px;
  background: white;
  border-left: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.panel-header {
  height: 50px;
  padding: 0 16px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  font-weight: 600;
  background: #f8f9fa;
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.debug-info {
  background: #f0f9ff;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 16px;
  border: 1px solid #bfdbfe;
}

.debug-info h4 {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: #1e40af;
}

.debug-info p {
  margin: 4px 0;
  font-size: 11px;
  color: #1e40af;
}

.property-section {
  margin-bottom: 24px;
}

.section-title {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 8px;
}

.property-item {
  margin-bottom: 12px;
}

.property-item label {
  display: block;
  margin-bottom: 4px;
  font-size: 12px;
  color: #606266;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
  padding: 40px 20px;
}

.empty-state p {
  margin-top: 16px;
  font-size: 14px;
}

.empty-state .hint {
  font-size: 12px;
  color: #c0c4cc;
  margin-top: 8px;
}
</style>
