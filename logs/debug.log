[2m2025-07-28 00:04:40.150[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    menu_id,parent_id,title,path,component,menu_type,sort_number,authority,icon,hide,meta,path_name,deleted,create_time,update_time    FROM  SYS_MENU     WHERE deleted=0     AND (parent_id = ? AND menu_type = ?)
[2m2025-07-28 00:04:40.220[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    menu_id,parent_id,title,path,component,menu_type,sort_number,authority,icon,hide,meta,path_name,deleted,create_time,update_time    FROM  SYS_MENU     WHERE deleted=0     AND (parent_id = ? AND menu_type = ?)
[2m2025-07-28 00:04:40.223[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT menu_id, parent_id, title, path, component, menu_type, sort_number, authority, icon, hide, meta, path_name, deleted, create_time, update_time FROM SYS_MENU WHERE deleted = 0 AND (parent_id = ? AND menu_type = ?)
[2m2025-07-28 00:04:40.242[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.S.selectList_mpCount          [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM SYS_MENU WHERE deleted = 0 AND (parent_id = ? AND menu_type = ?)
[2m2025-07-28 00:04:40.243[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.S.selectList_mpCount          [0;39m [2m:[0;39m ==> Parameters: 00000000000000000000000000000000(String), 0(Integer)
[2m2025-07-28 00:04:40.251[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.S.selectList_mpCount          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 00:04:40.270[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.mapper.SysMenuMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT menu_id, parent_id, title, path, component, menu_type, sort_number, authority, icon, hide, meta, path_name, deleted, create_time, update_time FROM SYS_MENU WHERE deleted = 0 AND (parent_id = ? AND menu_type = ?) ORDER BY sort_number ASC ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-07-28 00:04:40.270[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.mapper.SysMenuMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 00000000000000000000000000000000(String), 0(Integer), 10(Long), 0(Long)
[2m2025-07-28 00:04:40.280[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.mapper.SysMenuMapper.selectList [0;39m [2m:[0;39m <==      Total: 8
[2m2025-07-28 00:04:42.196[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    menu_id,parent_id,title,path,component,menu_type,sort_number,authority,icon,hide,meta,path_name,deleted,create_time,update_time    FROM  SYS_MENU     WHERE deleted=0     AND (parent_id = ?) ORDER BY sort_number ASC
[2m2025-07-28 00:04:42.208[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    menu_id,parent_id,title,path,component,menu_type,sort_number,authority,icon,hide,meta,path_name,deleted,create_time,update_time    FROM  SYS_MENU     WHERE deleted=0     AND (parent_id = ?) ORDER BY sort_number ASC
[2m2025-07-28 00:04:42.210[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT menu_id, parent_id, title, path, component, menu_type, sort_number, authority, icon, hide, meta, path_name, deleted, create_time, update_time FROM SYS_MENU WHERE deleted = 0 AND (parent_id = ?) ORDER BY sort_number ASC
[2m2025-07-28 00:04:42.211[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.s.mapper.SysMenuMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT menu_id, parent_id, title, path, component, menu_type, sort_number, authority, icon, hide, meta, path_name, deleted, create_time, update_time FROM SYS_MENU WHERE deleted = 0 AND (parent_id = ?) ORDER BY sort_number ASC
[2m2025-07-28 00:04:42.215[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.s.mapper.SysMenuMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 136D6E63407E740CE0630100007FB121(String)
[2m2025-07-28 00:04:42.223[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.s.mapper.SysMenuMapper.selectList [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:04:44.843[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT menu_id,parent_id,title,path,component,menu_type,sort_number,authority,icon,hide,meta,path_name,deleted,create_time,update_time FROM SYS_MENU WHERE menu_id=? AND deleted=0
[2m2025-07-28 00:04:44.845[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    menu_id,parent_id,title,path,component,menu_type,sort_number,authority,icon,hide,meta,path_name,deleted,create_time,update_time    FROM  SYS_MENU     WHERE deleted=0     AND (parent_id = ?) ORDER BY sort_number ASC
[2m2025-07-28 00:04:44.986[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT menu_id,parent_id,title,path,component,menu_type,sort_number,authority,icon,hide,meta,path_name,deleted,create_time,update_time FROM SYS_MENU WHERE menu_id=? AND deleted=0
[2m2025-07-28 00:04:44.989[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    menu_id,parent_id,title,path,component,menu_type,sort_number,authority,icon,hide,meta,path_name,deleted,create_time,update_time    FROM  SYS_MENU     WHERE deleted=0     AND (parent_id = ?) ORDER BY sort_number ASC
[2m2025-07-28 00:04:44.989[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT menu_id, parent_id, title, path, component, menu_type, sort_number, authority, icon, hide, meta, path_name, deleted, create_time, update_time FROM SYS_MENU WHERE menu_id = ? AND deleted = 0
[2m2025-07-28 00:04:44.989[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT menu_id, parent_id, title, path, component, menu_type, sort_number, authority, icon, hide, meta, path_name, deleted, create_time, update_time FROM SYS_MENU WHERE deleted = 0 AND (parent_id = ?) ORDER BY sort_number ASC
[2m2025-07-28 00:04:44.990[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.s.mapper.SysMenuMapper.selectById [0;39m [2m:[0;39m ==>  Preparing: SELECT menu_id, parent_id, title, path, component, menu_type, sort_number, authority, icon, hide, meta, path_name, deleted, create_time, update_time FROM SYS_MENU WHERE menu_id = ? AND deleted = 0
[2m2025-07-28 00:04:44.993[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.s.mapper.SysMenuMapper.selectById [0;39m [2m:[0;39m ==> Parameters: 136D6E63407E740CE0630100007FB121(String)
[2m2025-07-28 00:04:45.062[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.s.mapper.SysMenuMapper.selectById [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 00:04:45.063[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.s.mapper.SysMenuMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT menu_id, parent_id, title, path, component, menu_type, sort_number, authority, icon, hide, meta, path_name, deleted, create_time, update_time FROM SYS_MENU WHERE deleted = 0 AND (parent_id = ?) ORDER BY sort_number ASC
[2m2025-07-28 00:04:45.063[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.s.mapper.SysMenuMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 00000000000000000000000000000000(String)
[2m2025-07-28 00:04:45.129[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.s.mapper.SysMenuMapper.selectList [0;39m [2m:[0;39m <==      Total: 9
[2m2025-07-28 00:04:51.978[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: UPDATE SYS_MENU  SET parent_id=?, title=?, path=?, component=?, menu_type=?, sort_number=?,   hide=?  WHERE menu_id=? AND deleted=0
[2m2025-07-28 00:04:51.985[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: UPDATE SYS_MENU  SET parent_id=?, title=?, path=?, component=?, menu_type=?, sort_number=?,   hide=?  WHERE menu_id=? AND deleted=0
[2m2025-07-28 00:04:51.988[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: UPDATE SYS_MENU SET parent_id = ?, title = ?, path = ?, component = ?, menu_type = ?, sort_number = ?, hide = ? WHERE menu_id = ? AND deleted = 0
[2m2025-07-28 00:04:51.988[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.s.mapper.SysMenuMapper.updateById [0;39m [2m:[0;39m ==>  Preparing: UPDATE SYS_MENU SET parent_id = ?, title = ?, path = ?, component = ?, menu_type = ?, sort_number = ?, hide = ? WHERE menu_id = ? AND deleted = 0
[2m2025-07-28 00:04:51.988[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.s.mapper.SysMenuMapper.updateById [0;39m [2m:[0;39m ==> Parameters: 136D6E63407E740CE0630100007FB121(String), 打印设计测试(String), /bank-note/label-hiprint/label-designer-v2(String), /bank-note/label-hiprint/label-designer-v2(String), 1(Integer), 60(Integer), 0(Integer), e8c7c1c9120f8b26d9a841d6379624d0(String)
[2m2025-07-28 00:04:52.009[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.s.mapper.SysMenuMapper.updateById [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-28 00:04:52.146[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    menu_id,parent_id,title,path,component,menu_type,sort_number,authority,icon,hide,meta,path_name,deleted,create_time,update_time    FROM  SYS_MENU     WHERE deleted=0     AND (parent_id = ? AND menu_type = ?)
[2m2025-07-28 00:04:52.161[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    menu_id,parent_id,title,path,component,menu_type,sort_number,authority,icon,hide,meta,path_name,deleted,create_time,update_time    FROM  SYS_MENU     WHERE deleted=0     AND (parent_id = ? AND menu_type = ?)
[2m2025-07-28 00:04:52.161[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT menu_id, parent_id, title, path, component, menu_type, sort_number, authority, icon, hide, meta, path_name, deleted, create_time, update_time FROM SYS_MENU WHERE deleted = 0 AND (parent_id = ? AND menu_type = ?)
[2m2025-07-28 00:04:52.181[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.s.m.S.selectList_mpCount          [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM SYS_MENU WHERE deleted = 0 AND (parent_id = ? AND menu_type = ?)
[2m2025-07-28 00:04:52.181[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.s.m.S.selectList_mpCount          [0;39m [2m:[0;39m ==> Parameters: 00000000000000000000000000000000(String), 0(Integer)
[2m2025-07-28 00:04:52.186[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.s.m.S.selectList_mpCount          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 00:04:52.196[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.s.mapper.SysMenuMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT menu_id, parent_id, title, path, component, menu_type, sort_number, authority, icon, hide, meta, path_name, deleted, create_time, update_time FROM SYS_MENU WHERE deleted = 0 AND (parent_id = ? AND menu_type = ?) ORDER BY sort_number ASC ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-07-28 00:04:52.196[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.s.mapper.SysMenuMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 00000000000000000000000000000000(String), 0(Integer), 10(Long), 0(Long)
[2m2025-07-28 00:04:52.207[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.s.mapper.SysMenuMapper.selectList [0;39m [2m:[0;39m <==      Total: 8
[2m2025-07-28 00:05:02.523[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,ACCOUNT_NON_EXPIRED,ACCOUNT_NON_LOCKED,CREDENTIALS_NON_EXPIRED,ENABLED,PASSWORD,USERNAME,REAL_NAME,ONLINE_NAME,GENDER,ACTIVE_FLAG,ID_TYPE,ID_CODE,DEPT_NAME,TEL_MOBILE,EMAIL,START_TIME,END_TIME,PASSWORD_LAST_UPDATE_TIME    FROM  SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-07-28 00:05:02.534[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,ACCOUNT_NON_EXPIRED,ACCOUNT_NON_LOCKED,CREDENTIALS_NON_EXPIRED,ENABLED,PASSWORD,USERNAME,REAL_NAME,ONLINE_NAME,GENDER,ACTIVE_FLAG,ID_TYPE,ID_CODE,DEPT_NAME,TEL_MOBILE,EMAIL,START_TIME,END_TIME,PASSWORD_LAST_UPDATE_TIME    FROM  SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-07-28 00:05:02.536[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, ACCOUNT_NON_EXPIRED, ACCOUNT_NON_LOCKED, CREDENTIALS_NON_EXPIRED, ENABLED, PASSWORD, USERNAME, REAL_NAME, ONLINE_NAME, GENDER, ACTIVE_FLAG, ID_TYPE, ID_CODE, DEPT_NAME, TEL_MOBILE, EMAIL, START_TIME, END_TIME, PASSWORD_LAST_UPDATE_TIME FROM SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-07-28 00:05:02.537[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, ACCOUNT_NON_EXPIRED, ACCOUNT_NON_LOCKED, CREDENTIALS_NON_EXPIRED, ENABLED, PASSWORD, USERNAME, REAL_NAME, ONLINE_NAME, GENDER, ACTIVE_FLAG, ID_TYPE, ID_CODE, DEPT_NAME, TEL_MOBILE, EMAIL, START_TIME, END_TIME, PASSWORD_LAST_UPDATE_TIME FROM SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-07-28 00:05:02.541[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: admin(String), admin(String)
[2m2025-07-28 00:05:02.563[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 00:05:02.568[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT *
        FROM SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-07-28 00:05:02.576[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT *
        FROM SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-07-28 00:05:02.578[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT * FROM SYS_ROLE WHERE id IN (SELECT role_id FROM SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-07-28 00:05:02.579[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYS_ROLE WHERE id IN (SELECT role_id FROM SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-07-28 00:05:02.579[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==> Parameters: 13FF28E5AFA4D417E0630100007FE3FC(String)
[2m2025-07-28 00:05:02.585[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.S.selectByAccountId           [0;39m [2m:[0;39m <==      Total: 2
[2m2025-07-28 00:05:02.588[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT a.*
        FROM SYS_MENU a
         WHERE  a.menu_id IN (
            SELECT menu_id FROM SYS_ROLE_MENU WHERE role_id IN (
            SELECT ta.role_id FROM SYS_ACCOUNT_ROLE ta LEFT JOIN sys_role tb ON ta.role_id = tb.id
            WHERE ta.account_id = ? and ta.role_id = ?
            )
            )
            
            AND a.deleted = 0 
        ORDER BY a.sort_number
[2m2025-07-28 00:05:02.595[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT a.*
        FROM SYS_MENU a
         WHERE  a.menu_id IN (
            SELECT menu_id FROM SYS_ROLE_MENU WHERE role_id IN (
            SELECT ta.role_id FROM SYS_ACCOUNT_ROLE ta LEFT JOIN sys_role tb ON ta.role_id = tb.id
            WHERE ta.account_id = ? and ta.role_id = ?
            )
            )
            
            AND a.deleted = 0 
        ORDER BY a.sort_number
[2m2025-07-28 00:05:02.596[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT a.* FROM SYS_MENU a WHERE a.menu_id IN (SELECT menu_id FROM SYS_ROLE_MENU WHERE role_id IN (SELECT ta.role_id FROM SYS_ACCOUNT_ROLE ta LEFT JOIN sys_role tb ON ta.role_id = tb.id WHERE ta.account_id = ? AND ta.role_id = ?)) AND a.deleted = 0 ORDER BY a.sort_number
[2m2025-07-28 00:05:02.597[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.R.listMenuByAccount           [0;39m [2m:[0;39m ==>  Preparing: SELECT a.* FROM SYS_MENU a WHERE a.menu_id IN (SELECT menu_id FROM SYS_ROLE_MENU WHERE role_id IN (SELECT ta.role_id FROM SYS_ACCOUNT_ROLE ta LEFT JOIN sys_role tb ON ta.role_id = tb.id WHERE ta.account_id = ? AND ta.role_id = ?)) AND a.deleted = 0 ORDER BY a.sort_number
[2m2025-07-28 00:05:02.597[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.R.listMenuByAccount           [0;39m [2m:[0;39m ==> Parameters: 13FF28E5AFA4D417E0630100007FE3FC(String), 36d5ca2b3906a97f4274ffb8e8a36210(String)
[2m2025-07-28 00:05:02.625[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.R.listMenuByAccount           [0;39m [2m:[0;39m <==      Total: 71
[2m2025-07-28 00:05:02.964[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    menu_id,parent_id,title,path,component,menu_type,sort_number,authority,icon,hide,meta,path_name,deleted,create_time,update_time    FROM  SYS_MENU     WHERE deleted=0     AND (parent_id = ? AND menu_type = ?)
[2m2025-07-28 00:05:02.976[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    menu_id,parent_id,title,path,component,menu_type,sort_number,authority,icon,hide,meta,path_name,deleted,create_time,update_time    FROM  SYS_MENU     WHERE deleted=0     AND (parent_id = ? AND menu_type = ?)
[2m2025-07-28 00:05:02.979[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT menu_id, parent_id, title, path, component, menu_type, sort_number, authority, icon, hide, meta, path_name, deleted, create_time, update_time FROM SYS_MENU WHERE deleted = 0 AND (parent_id = ? AND menu_type = ?)
[2m2025-07-28 00:05:03.009[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.s.m.S.selectList_mpCount          [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM SYS_MENU WHERE deleted = 0 AND (parent_id = ? AND menu_type = ?)
[2m2025-07-28 00:05:03.010[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.s.m.S.selectList_mpCount          [0;39m [2m:[0;39m ==> Parameters: 00000000000000000000000000000000(String), 0(Integer)
[2m2025-07-28 00:05:03.015[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.s.m.S.selectList_mpCount          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 00:05:03.025[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.s.mapper.SysMenuMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT menu_id, parent_id, title, path, component, menu_type, sort_number, authority, icon, hide, meta, path_name, deleted, create_time, update_time FROM SYS_MENU WHERE deleted = 0 AND (parent_id = ? AND menu_type = ?) ORDER BY sort_number ASC ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-07-28 00:05:03.026[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.s.mapper.SysMenuMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 00000000000000000000000000000000(String), 0(Integer), 10(Long), 0(Long)
[2m2025-07-28 00:05:03.035[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.s.mapper.SysMenuMapper.selectList [0;39m [2m:[0;39m <==      Total: 8
[2m2025-07-28 00:05:48.306[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:05:48.335[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:05:48.344[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:05:48.346[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:05:48.350[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:05:48.364[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:05:57.402[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:05:57.415[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:05:57.417[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:05:57.418[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:05:57.420[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:05:57.428[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:07:34.601[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:07:34.638[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:07:34.639[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:07:34.648[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:07:34.649[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:07:34.655[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:07:47.442[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:07:47.458[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:07:47.467[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:07:47.469[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:07:47.471[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:07:47.480[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:08:43.607[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:08:43.656[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:08:43.685[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:08:43.686[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:08:43.687[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:08:43.695[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:08:53.710[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:08:53.745[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:08:53.757[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:08:53.758[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:08:53.760[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:08:53.778[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:09:17.593[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:09:17.639[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:09:17.640[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:09:17.640[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:09:17.641[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:09:17.657[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:09:29.346[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:09:29.375[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:09:29.376[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:09:29.380[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:09:29.381[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:09:29.389[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:10:13.493[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:10:13.504[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:10:13.505[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:10:13.505[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:10:13.508[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:10:13.515[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:11:06.914[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:11:06.945[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:11:06.948[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:11:06.958[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:11:06.964[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:11:06.980[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:11:19.314[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:11:19.330[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:11:19.333[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:11:19.335[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:11:19.336[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:11:19.405[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:11:30.323[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:11:30.340[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:11:30.340[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:11:30.341[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:11:30.342[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:11:30.351[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:11:41.217[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:11:41.251[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:11:41.252[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:11:41.281[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:11:41.303[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:11:41.327[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:11:56.494[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:11:56.535[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:11:56.543[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:11:56.549[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:11:56.550[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:11:56.559[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:13:22.321[0;39m [32m INFO[0;39m [35m69539[0;39m [2m---[0;39m [2m[nexc.home:27017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Exception in monitor thread while connecting to server paynexc.home:27017

com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.InternalStreamConnection.createReadTimeoutException(InternalStreamConnection.java:819)
	at com.mongodb.internal.connection.InternalStreamConnection.translateReadException(InternalStreamConnection.java:807)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:857)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:517)
	at com.mongodb.internal.connection.InternalStreamConnection.receive(InternalStreamConnection.java:469)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.lookupServerDescription(DefaultServerMonitor.java:249)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.run(DefaultServerMonitor.java:176)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.base/sun.nio.ch.NioSocketImpl.timedRead(NioSocketImpl.java:288)
	at java.base/sun.nio.ch.NioSocketImpl.implRead(NioSocketImpl.java:314)
	at java.base/sun.nio.ch.NioSocketImpl.read(NioSocketImpl.java:355)
	at java.base/sun.nio.ch.NioSocketImpl$1.read(NioSocketImpl.java:808)
	at java.base/java.net.Socket$SocketInputStream.read(Socket.java:976)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:182)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:824)
	... 4 common frames omitted

[2m2025-07-28 00:13:25.561[0;39m [32m INFO[0;39m [35m69539[0;39m [2m---[0;39m [2m[nexc.home:27017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Monitor thread successfully connected to server with description ServerDescription{address=paynexc.home:27017, type=STANDALONE, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=21, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=6082917, minRoundTripTimeNanos=0}
[2m2025-07-28 00:13:32.501[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:13:32.591[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:13:32.599[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:13:32.623[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:13:32.624[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:13:32.629[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:14:59.241[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 00:14:59.265[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 00:14:59.273[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 00:14:59.289[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 00:14:59.290[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-28 00:14:59.308[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-28 00:14:59.347[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:14:59.362[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:14:59.362[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:14:59.366[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:14:59.367[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:14:59.378[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:15:07.476[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 00:15:07.491[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 00:15:07.497[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 00:15:07.497[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 00:15:07.498[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-28 00:15:07.514[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-28 00:15:07.550[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:15:07.561[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:15:07.561[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:15:07.561[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:15:07.564[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:15:07.570[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:15:08.083[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:15:08.098[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:15:08.102[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:15:08.103[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:15:08.103[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:15:08.112[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:16:15.829[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 00:16:15.839[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 00:16:15.842[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 00:16:15.848[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 00:16:15.849[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-28 00:16:15.861[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-28 00:16:15.887[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:16:15.896[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:16:15.898[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:16:15.898[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:16:15.898[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:16:15.904[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:16:18.803[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:16:19.454[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:16:19.457[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:16:19.457[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:16:19.457[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:16:19.463[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:16:59.933[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: UPDATE LABEL_TEMPLATE  SET TEMPLATE_NAME=?, TEMPLATE_TYPE=?, LAYOUT_CONFIG=?,    IS_DEFAULT=?,   UPDATE_TIME=?, STATUS=?  WHERE ID=?
[2m2025-07-28 00:16:59.943[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: UPDATE LABEL_TEMPLATE  SET TEMPLATE_NAME=?, TEMPLATE_TYPE=?, LAYOUT_CONFIG=?,    IS_DEFAULT=?,   UPDATE_TIME=?, STATUS=?  WHERE ID=?
[2m2025-07-28 00:16:59.945[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: UPDATE LABEL_TEMPLATE SET TEMPLATE_NAME = ?, TEMPLATE_TYPE = ?, LAYOUT_CONFIG = ?, IS_DEFAULT = ?, UPDATE_TIME = ?, STATUS = ? WHERE ID = ?
[2m2025-07-28 00:16:59.945[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.L.updateById                  [0;39m [2m:[0;39m ==>  Preparing: UPDATE LABEL_TEMPLATE SET TEMPLATE_NAME = ?, TEMPLATE_TYPE = ?, LAYOUT_CONFIG = ?, IS_DEFAULT = ?, UPDATE_TIME = ?, STATUS = ? WHERE ID = ?
[2m2025-07-28 00:16:59.947[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.L.updateById                  [0;39m [2m:[0;39m ==> Parameters: 大签191x26(String), CUSTOM(String), {"panels":[{"index":0,"name":1,"height":50,"width":191,"paperHeader":0,"paperFooter":141.73228346456693,"printElements":[],"paperNumberLeft":511,"paperNumberTop":119,"paperNumberContinue":true,"watermarkOptions":{},"panelLayoutOptions":{}},{"index":1,"name":2,"paperType":"A4","height":297,"width":210,"paperHeader":0,"paperFooter":841.8897637795277,"printElements":[],"paperNumberContinue":true,"watermarkOptions":{},"panelLayoutOptions":{}}]}(String), false(Boolean), 2025-07-28T00:16:59.929813(LocalDateTime), ACTIVE(String), cb6b4723a01c7e32a8e175d965ac3ef4(String)
[2m2025-07-28 00:16:59.966[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.L.updateById                  [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-28 00:16:59.995[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 00:17:00.003[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 00:17:00.005[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 00:17:00.006[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 00:17:00.006[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-28 00:17:00.033[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-28 00:17:28.865[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: UPDATE LABEL_TEMPLATE  SET TEMPLATE_NAME=?, TEMPLATE_TYPE=?, LAYOUT_CONFIG=?,    IS_DEFAULT=?,   UPDATE_TIME=?, STATUS=?  WHERE ID=?
[2m2025-07-28 00:17:28.872[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: UPDATE LABEL_TEMPLATE  SET TEMPLATE_NAME=?, TEMPLATE_TYPE=?, LAYOUT_CONFIG=?,    IS_DEFAULT=?,   UPDATE_TIME=?, STATUS=?  WHERE ID=?
[2m2025-07-28 00:17:28.875[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: UPDATE LABEL_TEMPLATE SET TEMPLATE_NAME = ?, TEMPLATE_TYPE = ?, LAYOUT_CONFIG = ?, IS_DEFAULT = ?, UPDATE_TIME = ?, STATUS = ? WHERE ID = ?
[2m2025-07-28 00:17:28.875[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.L.updateById                  [0;39m [2m:[0;39m ==>  Preparing: UPDATE LABEL_TEMPLATE SET TEMPLATE_NAME = ?, TEMPLATE_TYPE = ?, LAYOUT_CONFIG = ?, IS_DEFAULT = ?, UPDATE_TIME = ?, STATUS = ? WHERE ID = ?
[2m2025-07-28 00:17:28.875[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.L.updateById                  [0;39m [2m:[0;39m ==> Parameters: 大签191x26(String), CUSTOM(String), {"panels":[{"index":0,"name":1,"height":50,"width":191,"paperHeader":0,"paperFooter":141.73228346456693,"printElements":[{"options":{"left":0,"top":0,"height":136.5,"width":115.5,"right":138,"bottom":136.5,"vCenter":69,"hCenter":68.25},"printElementType":{"title":"矩形","type":"rect"}}],"paperNumberLeft":511,"paperNumberTop":119,"paperNumberContinue":true,"watermarkOptions":{},"panelLayoutOptions":{}},{"index":1,"name":2,"paperType":"A4","height":297,"width":210,"paperHeader":0,"paperFooter":841.8897637795277,"printElements":[],"paperNumberContinue":true,"watermarkOptions":{},"panelLayoutOptions":{}}]}(String), false(Boolean), 2025-07-28T00:17:28.864207(LocalDateTime), ACTIVE(String), cb6b4723a01c7e32a8e175d965ac3ef4(String)
[2m2025-07-28 00:17:28.887[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.L.updateById                  [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-28 00:17:28.916[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 00:17:28.923[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 00:17:28.924[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 00:17:28.926[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 00:17:28.927[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-28 00:17:28.937[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-28 00:39:16.895[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 00:39:16.934[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 00:39:16.939[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 00:39:16.950[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 00:39:16.953[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-28 00:39:16.967[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-28 00:39:17.005[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:39:17.018[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:39:17.018[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:39:17.022[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:39:17.022[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:39:17.033[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:40:44.140[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 00:40:44.197[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 00:40:44.199[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 00:40:44.210[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 00:40:44.211[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-28 00:40:44.222[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-28 00:40:44.249[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:40:44.255[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:40:44.257[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:40:44.257[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:40:44.257[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:40:44.262[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:40:46.174[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:40:46.181[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:40:46.183[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:40:46.184[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:40:46.184[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:40:46.190[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:46:12.969[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 00:46:12.999[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 00:46:13.002[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 00:46:13.010[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 00:46:13.012[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-28 00:46:13.026[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-28 00:46:13.055[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:46:13.062[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:46:13.064[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:46:13.064[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:46:13.065[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:46:13.070[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:46:14.562[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:46:14.570[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:46:14.571[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:46:14.572[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:46:14.572[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:46:14.577[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:55:43.403[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 00:55:43.417[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 00:55:43.420[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 00:55:43.488[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 00:55:43.489[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-28 00:55:43.625[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-28 00:55:43.660[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:55:43.667[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:55:43.669[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:55:43.669[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:55:43.669[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:55:43.675[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:55:47.116[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:55:47.134[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:55:47.136[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:55:47.140[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 00:55:47.141[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 00:55:47.152[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 01:07:20.694[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 01:07:20.718[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-28 01:07:20.720[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 01:07:20.730[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-28 01:07:20.730[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-28 01:07:20.752[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-28 01:07:20.781[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:07:20.788[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:07:20.790[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:07:20.791[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:07:20.791[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 01:07:20.797[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 01:07:22.083[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:07:22.090[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:07:22.093[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:07:22.093[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-28 01:07:22.093[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-28 01:07:22.101[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
